spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # druid相关配置
    druid:
      #  如果存在多个数据源，监控的时候可以通过名字来区分开来。如果没有配置，将会生成一个名字，格式是："DataSource-" + System.identityHashCode(this)
      name: DataSource-backlog
      # 基本属性
      driverClassName: com.mysql.jdbc.Driver
      url: ***************************************************************************************************************************
      username: root
      password: sjyt_cywKZHAl
      # 配置初始化大小/最小/最大
      initial-size: 1
      min-idle: 1
      max-active: 20
      # 获取连接等待超时时间
      max-wait: 10000
      # 间隔多久进行一次检测，检测需要关闭的空闲连接
      time-between-eviction-runs-millis: 60000
      # 一个连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      # 监控统计拦截的filters
      filters: stat
      # 这里不注掉会有循环依赖检测的问题
      # aop-patterns: com.javayh.druid.*
      web-stat-filter:
        # 是否启用StatFilter默认值true
        enabled: true
        url-pattern: /*
        exclusions: /druid/*,*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico
        session-stat-enable: true
        session-stat-max-count: 10
      stat-view-servlet:
        # 是否启用statViewServlet配置
        enabled: true
        # 访问监控页面
        url-pattern: "/druid/*"
        # 禁止手动重置监控数据
        reset-enable: false
        # 设置监控页面的登陆名
        login-username: admin
        # 设置监控页面的登陆密码
        login-password: admin
  redis:
    host: sjyt-redis.sjyt-dev
    #数据库索引
    database: 8
    port: 6379
    password: sjyt_cywKZHAl
    #连接超时时间
    timeout: 5000
