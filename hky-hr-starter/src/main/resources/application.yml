server:
  servlet:
    context-path: /hr-service
  port: 8081
spring:
  profiles:
    active: dev
  application:
    name: hr-service
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: ${SPRING_JPA_SHOW_SQL:true}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false