package com.hky.hr.organization.service;

import com.hky.hr.organization.dto.OrganizationDTO;
import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.Status;
import com.hky.hr.organization.repository.OrganizationRepository;
import com.hky.hr.organization.repository.PositionRepository;
import com.hky.hr.organization.service.impl.OrganizationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 组织管理服务测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class OrganizationServiceTest {
    
    @Mock
    private OrganizationRepository organizationRepository;
    
    @Mock
    private PositionRepository positionRepository;
    
    @InjectMocks
    private OrganizationServiceImpl organizationService;
    
    private OrganizationDTO testOrganizationDTO;
    private Organization testOrganization;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        testOrganizationDTO = new OrganizationDTO();
        testOrganizationDTO.setOrgCode("TEST001");
        testOrganizationDTO.setOrgName("测试组织");
        testOrganizationDTO.setOrgShortName("测试");
        testOrganizationDTO.setOrgType(OrganizationType.DEPARTMENT);
        testOrganizationDTO.setStatus(Status.ACTIVE);
        testOrganizationDTO.setLeader("张三");
        testOrganizationDTO.setPhone("13800138000");
        testOrganizationDTO.setEmail("<EMAIL>");
        testOrganizationDTO.setAddress("杭州市");
        testOrganizationDTO.setEstablishmentDate(LocalDate.now());
        
        testOrganization = testOrganizationDTO.toEntity();
        testOrganization.setId(1L);
        testOrganization.setOrgLevel(1);
        testOrganization.setParentPath("/");
        testOrganization.setSortOrder(1);
    }
    
    @Test
    void testCreateOrganization_Success() {
        // Given
        when(organizationRepository.existsByOrgCodeAndDeletedFalse(anyString())).thenReturn(false);
        when(organizationRepository.findMaxSortOrderByParentId(any())).thenReturn(0);
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        
        // When
        OrganizationDTO result = organizationService.createOrganization(testOrganizationDTO);
        
        // Then
        assertNotNull(result);
        assertEquals(testOrganizationDTO.getOrgCode(), result.getOrgCode());
        assertEquals(testOrganizationDTO.getOrgName(), result.getOrgName());
        assertEquals(testOrganizationDTO.getOrgType(), result.getOrgType());
        
        verify(organizationRepository).existsByOrgCodeAndDeletedFalse(testOrganizationDTO.getOrgCode());
        verify(organizationRepository).save(any(Organization.class));
    }
    
    @Test
    void testCreateOrganization_DuplicateCode() {
        // Given
        when(organizationRepository.existsByOrgCodeAndDeletedFalse(anyString())).thenReturn(true);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> organizationService.createOrganization(testOrganizationDTO)
        );
        
        assertEquals("组织编码已存在: " + testOrganizationDTO.getOrgCode(), exception.getMessage());
        verify(organizationRepository, never()).save(any(Organization.class));
    }
    
    @Test
    void testGetOrganizationById_Success() {
        // Given
        Long organizationId = 1L;
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        when(positionRepository.countByOrganizationIdAndDeletedFalse(organizationId)).thenReturn(5L);
        
        // When
        OrganizationDTO result = organizationService.getOrganizationById(organizationId);
        
        // Then
        assertNotNull(result);
        assertEquals(testOrganization.getId(), result.getId());
        assertEquals(testOrganization.getOrgCode(), result.getOrgCode());
        assertEquals(testOrganization.getOrgName(), result.getOrgName());
        assertEquals(5L, result.getPositionCount());
        
        verify(organizationRepository).findById(organizationId);
        verify(positionRepository).countByOrganizationIdAndDeletedFalse(organizationId);
    }
    
    @Test
    void testGetOrganizationById_NotFound() {
        // Given
        Long organizationId = 999L;
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.empty());
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> organizationService.getOrganizationById(organizationId)
        );
        
        assertEquals("组织不存在: " + organizationId, exception.getMessage());
        verify(organizationRepository).findById(organizationId);
        verify(positionRepository, never()).countByOrganizationIdAndDeletedFalse(any());
    }
    
    @Test
    void testGetOrganizationById_Deleted() {
        // Given
        Long organizationId = 1L;
        testOrganization.setDeleted(true);
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> organizationService.getOrganizationById(organizationId)
        );
        
        assertEquals("组织已被删除: " + organizationId, exception.getMessage());
        verify(organizationRepository).findById(organizationId);
        verify(positionRepository, never()).countByOrganizationIdAndDeletedFalse(any());
    }
    
    @Test
    void testUpdateOrganization_Success() {
        // Given
        Long organizationId = 1L;
        OrganizationDTO updateDTO = new OrganizationDTO();
        updateDTO.setOrgCode("TEST002");
        updateDTO.setOrgName("更新后的组织");
        updateDTO.setOrgType(OrganizationType.COLLEGE);
        updateDTO.setStatus(Status.ACTIVE);
        
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.existsByOrgCodeAndDeletedFalseAndIdNot(anyString(), any())).thenReturn(false);
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        
        // When
        OrganizationDTO result = organizationService.updateOrganization(organizationId, updateDTO);
        
        // Then
        assertNotNull(result);
        verify(organizationRepository).findById(organizationId);
        verify(organizationRepository).existsByOrgCodeAndDeletedFalseAndIdNot(updateDTO.getOrgCode(), organizationId);
        verify(organizationRepository).save(any(Organization.class));
    }
    
    @Test
    void testDeleteOrganization_Success() {
        // Given
        Long organizationId = 1L;
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.countByParentIdAndDeletedFalse(organizationId)).thenReturn(0L);
        when(positionRepository.countByOrganizationIdAndDeletedFalse(organizationId)).thenReturn(0L);
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        
        // When
        organizationService.deleteOrganization(organizationId);
        
        // Then
        verify(organizationRepository).findById(organizationId);
        verify(organizationRepository).countByParentIdAndDeletedFalse(organizationId);
        verify(positionRepository).countByOrganizationIdAndDeletedFalse(organizationId);
        verify(organizationRepository).save(any(Organization.class));
    }
    
    @Test
    void testDeleteOrganization_HasChildren() {
        // Given
        Long organizationId = 1L;
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.countByParentIdAndDeletedFalse(organizationId)).thenReturn(1L);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> organizationService.deleteOrganization(organizationId)
        );
        
        assertEquals("存在子组织，无法删除", exception.getMessage());
        verify(organizationRepository, never()).save(any(Organization.class));
    }
    
    @Test
    void testDeleteOrganization_HasPositions() {
        // Given
        Long organizationId = 1L;
        when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.countByParentIdAndDeletedFalse(organizationId)).thenReturn(0L);
        when(positionRepository.countByOrganizationIdAndDeletedFalse(organizationId)).thenReturn(1L);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> organizationService.deleteOrganization(organizationId)
        );
        
        assertEquals("存在岗位，无法删除", exception.getMessage());
        verify(organizationRepository, never()).save(any(Organization.class));
    }
    
    @Test
    void testExistsByOrgCode_WithExcludeId() {
        // Given
        String orgCode = "TEST001";
        Long excludeId = 1L;
        when(organizationRepository.existsByOrgCodeAndDeletedFalseAndIdNot(orgCode, excludeId)).thenReturn(true);
        
        // When
        boolean result = organizationService.existsByOrgCode(orgCode, excludeId);
        
        // Then
        assertTrue(result);
        verify(organizationRepository).existsByOrgCodeAndDeletedFalseAndIdNot(orgCode, excludeId);
    }
    
    @Test
    void testExistsByOrgCode_WithoutExcludeId() {
        // Given
        String orgCode = "TEST001";
        when(organizationRepository.existsByOrgCodeAndDeletedFalse(orgCode)).thenReturn(false);
        
        // When
        boolean result = organizationService.existsByOrgCode(orgCode, null);
        
        // Then
        assertFalse(result);
        verify(organizationRepository).existsByOrgCodeAndDeletedFalse(orgCode);
    }
}
