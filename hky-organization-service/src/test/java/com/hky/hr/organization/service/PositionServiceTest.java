package com.hky.hr.organization.service;

import com.hky.hr.organization.dto.PositionDTO;
import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.entity.Position;
import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.PositionType;
import com.hky.hr.organization.enums.Status;
import com.hky.hr.organization.repository.OrganizationRepository;
import com.hky.hr.organization.repository.PositionRepository;
import com.hky.hr.organization.service.impl.PositionServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 岗位管理服务测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class PositionServiceTest {
    
    @Mock
    private PositionRepository positionRepository;
    
    @Mock
    private OrganizationRepository organizationRepository;
    
    @InjectMocks
    private PositionServiceImpl positionService;
    
    private PositionDTO testPositionDTO;
    private Position testPosition;
    private Organization testOrganization;
    
    @BeforeEach
    void setUp() {
        // 准备测试组织数据
        testOrganization = new Organization();
        testOrganization.setId(1L);
        testOrganization.setOrgCode("ORG001");
        testOrganization.setOrgName("测试组织");
        testOrganization.setOrgType(OrganizationType.DEPARTMENT);
        testOrganization.setStatus(Status.ACTIVE);
        testOrganization.setDeleted(false);
        
        // 准备测试岗位数据
        testPositionDTO = new PositionDTO();
        testPositionDTO.setPositionCode("POS001");
        testPositionDTO.setPositionName("测试岗位");
        testPositionDTO.setOrganizationId(1L);
        testPositionDTO.setPositionType(PositionType.MANAGEMENT);
        testPositionDTO.setPositionLevel("中级");
        testPositionDTO.setResponsibilities("负责测试工作");
        testPositionDTO.setRequirements("本科以上学历");
        testPositionDTO.setMinSalary(new BigDecimal("5000.00"));
        testPositionDTO.setMaxSalary(new BigDecimal("8000.00"));
        testPositionDTO.setHeadcount(2);
        testPositionDTO.setCurrentCount(1);
        testPositionDTO.setStatus(Status.ACTIVE);
        
        testPosition = testPositionDTO.toEntity();
        testPosition.setId(1L);
        testPosition.setSortOrder(1);
    }
    
    @Test
    void testCreatePosition_Success() {
        // Given
        when(positionRepository.existsByPositionCodeAndDeletedFalse(anyString())).thenReturn(false);
        when(organizationRepository.findById(any())).thenReturn(Optional.of(testOrganization));
        when(positionRepository.findMaxSortOrderByOrganizationId(any())).thenReturn(0);
        when(positionRepository.save(any(Position.class))).thenReturn(testPosition);
        
        // When
        PositionDTO result = positionService.createPosition(testPositionDTO);
        
        // Then
        assertNotNull(result);
        assertEquals(testPositionDTO.getPositionCode(), result.getPositionCode());
        assertEquals(testPositionDTO.getPositionName(), result.getPositionName());
        assertEquals(testPositionDTO.getPositionType(), result.getPositionType());
        
        verify(positionRepository).existsByPositionCodeAndDeletedFalse(testPositionDTO.getPositionCode());
        verify(organizationRepository).findById(testPositionDTO.getOrganizationId());
        verify(positionRepository).save(any(Position.class));
    }
    
    @Test
    void testCreatePosition_DuplicateCode() {
        // Given
        when(positionRepository.existsByPositionCodeAndDeletedFalse(anyString())).thenReturn(true);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.createPosition(testPositionDTO)
        );
        
        assertEquals("岗位编码已存在: " + testPositionDTO.getPositionCode(), exception.getMessage());
        verify(positionRepository, never()).save(any(Position.class));
    }
    
    @Test
    void testCreatePosition_OrganizationNotFound() {
        // Given
        when(positionRepository.existsByPositionCodeAndDeletedFalse(anyString())).thenReturn(false);
        when(organizationRepository.findById(any())).thenReturn(Optional.empty());
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.createPosition(testPositionDTO)
        );
        
        assertEquals("所属组织不存在: " + testPositionDTO.getOrganizationId(), exception.getMessage());
        verify(positionRepository, never()).save(any(Position.class));
    }
    
    @Test
    void testCreatePosition_OrganizationDeleted() {
        // Given
        testOrganization.setDeleted(true);
        when(positionRepository.existsByPositionCodeAndDeletedFalse(anyString())).thenReturn(false);
        when(organizationRepository.findById(any())).thenReturn(Optional.of(testOrganization));
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.createPosition(testPositionDTO)
        );
        
        assertEquals("所属组织已被删除: " + testPositionDTO.getOrganizationId(), exception.getMessage());
        verify(positionRepository, never()).save(any(Position.class));
    }
    
    @Test
    void testGetPositionById_Success() {
        // Given
        Long positionId = 1L;
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        
        // When
        PositionDTO result = positionService.getPositionById(positionId);
        
        // Then
        assertNotNull(result);
        assertEquals(testPosition.getId(), result.getId());
        assertEquals(testPosition.getPositionCode(), result.getPositionCode());
        assertEquals(testPosition.getPositionName(), result.getPositionName());
        
        verify(positionRepository).findById(positionId);
    }
    
    @Test
    void testGetPositionById_NotFound() {
        // Given
        Long positionId = 999L;
        when(positionRepository.findById(positionId)).thenReturn(Optional.empty());
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.getPositionById(positionId)
        );
        
        assertEquals("岗位不存在: " + positionId, exception.getMessage());
        verify(positionRepository).findById(positionId);
    }
    
    @Test
    void testGetPositionById_Deleted() {
        // Given
        Long positionId = 1L;
        testPosition.setDeleted(true);
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.getPositionById(positionId)
        );
        
        assertEquals("岗位已被删除: " + positionId, exception.getMessage());
        verify(positionRepository).findById(positionId);
    }
    
    @Test
    void testUpdatePosition_Success() {
        // Given
        Long positionId = 1L;
        PositionDTO updateDTO = new PositionDTO();
        updateDTO.setPositionCode("POS002");
        updateDTO.setPositionName("更新后的岗位");
        updateDTO.setOrganizationId(1L);
        updateDTO.setPositionType(PositionType.TEACHING);
        updateDTO.setStatus(Status.ACTIVE);
        
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        when(positionRepository.existsByPositionCodeAndDeletedFalseAndIdNot(anyString(), any())).thenReturn(false);
        when(positionRepository.save(any(Position.class))).thenReturn(testPosition);
        
        // When
        PositionDTO result = positionService.updatePosition(positionId, updateDTO);
        
        // Then
        assertNotNull(result);
        verify(positionRepository).findById(positionId);
        verify(positionRepository).existsByPositionCodeAndDeletedFalseAndIdNot(updateDTO.getPositionCode(), positionId);
        verify(positionRepository).save(any(Position.class));
    }
    
    @Test
    void testDeletePosition_Success() {
        // Given
        Long positionId = 1L;
        testPosition.setCurrentCount(0);
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        when(positionRepository.save(any(Position.class))).thenReturn(testPosition);
        
        // When
        positionService.deletePosition(positionId);
        
        // Then
        verify(positionRepository).findById(positionId);
        verify(positionRepository).save(any(Position.class));
    }
    
    @Test
    void testDeletePosition_HasCurrentCount() {
        // Given
        Long positionId = 1L;
        testPosition.setCurrentCount(1);
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.deletePosition(positionId)
        );
        
        assertEquals("岗位有在职人员，无法删除", exception.getMessage());
        verify(positionRepository, never()).save(any(Position.class));
    }
    
    @Test
    void testUpdatePositionCount_Success() {
        // Given
        Long positionId = 1L;
        Integer newCurrentCount = 2;
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        when(positionRepository.save(any(Position.class))).thenReturn(testPosition);
        
        // When
        PositionDTO result = positionService.updatePositionCount(positionId, newCurrentCount);
        
        // Then
        assertNotNull(result);
        verify(positionRepository).findById(positionId);
        verify(positionRepository).save(any(Position.class));
    }
    
    @Test
    void testUpdatePositionCount_NegativeCount() {
        // Given
        Long positionId = 1L;
        Integer negativeCount = -1;
        when(positionRepository.findById(positionId)).thenReturn(Optional.of(testPosition));
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> positionService.updatePositionCount(positionId, negativeCount)
        );
        
        assertEquals("当前人数不能小于0", exception.getMessage());
        verify(positionRepository, never()).save(any(Position.class));
    }
    
    @Test
    void testExistsByPositionCode_WithExcludeId() {
        // Given
        String positionCode = "POS001";
        Long excludeId = 1L;
        when(positionRepository.existsByPositionCodeAndDeletedFalseAndIdNot(positionCode, excludeId)).thenReturn(true);
        
        // When
        boolean result = positionService.existsByPositionCode(positionCode, excludeId);
        
        // Then
        assertTrue(result);
        verify(positionRepository).existsByPositionCodeAndDeletedFalseAndIdNot(positionCode, excludeId);
    }
    
    @Test
    void testExistsByPositionCode_WithoutExcludeId() {
        // Given
        String positionCode = "POS001";
        when(positionRepository.existsByPositionCodeAndDeletedFalse(positionCode)).thenReturn(false);
        
        // When
        boolean result = positionService.existsByPositionCode(positionCode, null);
        
        // Then
        assertFalse(result);
        verify(positionRepository).existsByPositionCodeAndDeletedFalse(positionCode);
    }
}
