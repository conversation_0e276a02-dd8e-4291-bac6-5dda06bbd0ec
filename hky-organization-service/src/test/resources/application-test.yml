server:
  port: 8102

spring:
  application:
    name: hky-organization-service-test
  
  # 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置（测试环境禁用）
  data:
    redis:
      repositories:
        enabled: false

# 日志配置
logging:
  level:
    com.hky.hr.organization: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# API文档配置
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
