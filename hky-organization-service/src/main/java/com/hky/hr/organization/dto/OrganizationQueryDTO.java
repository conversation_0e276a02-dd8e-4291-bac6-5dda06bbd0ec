package com.hky.hr.organization.dto;

import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组织查询条件DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationQueryDTO {
    
    /**
     * 组织名称（模糊查询）
     */
    private String orgName;
    
    /**
     * 组织编码
     */
    private String orgCode;
    
    /**
     * 组织类型
     */
    private OrganizationType orgType;
    
    /**
     * 状态
     */
    private Status status;
    
    /**
     * 父级组织ID
     */
    private Long parentId;
    
    /**
     * 组织层级
     */
    private Integer orgLevel;
    
    /**
     * 负责人
     */
    private String leader;
    
    /**
     * 是否虚拟部门
     */
    private Boolean isVirtual;
    
    /**
     * 是否临时机构
     */
    private Boolean isTemporary;
    
    /**
     * 页码（从0开始）
     */
    private Integer page = 0;
    
    /**
     * 每页大小
     */
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sortBy = "sortOrder";
    
    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection = "asc";
}
