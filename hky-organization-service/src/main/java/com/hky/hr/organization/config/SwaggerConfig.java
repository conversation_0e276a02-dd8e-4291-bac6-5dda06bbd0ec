package com.hky.hr.organization.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class SwaggerConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("杭科院人事管理系统 - 组织管理服务")
                .description("提供组织架构管理、部门管理、岗位管理等功能的REST API")
                .version("1.0.0")
                .contact(new Contact()
                    .name("HKY-HR-System")
                    .email("<EMAIL>")
                    .url("https://www.hky.edu.cn"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT")));
    }
}
