package com.hky.hr.organization.service.impl;

import com.hky.hr.organization.dto.PositionDTO;
import com.hky.hr.organization.dto.PositionQueryDTO;
import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.entity.Position;
import com.hky.hr.organization.repository.OrganizationRepository;
import com.hky.hr.organization.repository.PositionRepository;
import com.hky.hr.organization.service.PositionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 岗位管理服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PositionServiceImpl implements PositionService {
    
    private final PositionRepository positionRepository;
    private final OrganizationRepository organizationRepository;
    
    @Override
    @Transactional
    public PositionDTO createPosition(PositionDTO positionDTO) {
        log.info("创建岗位: {}", positionDTO.getPositionName());
        
        // 检查岗位编码是否已存在
        if (positionRepository.existsByPositionCodeAndDeletedFalse(positionDTO.getPositionCode())) {
            throw new IllegalArgumentException("岗位编码已存在: " + positionDTO.getPositionCode());
        }
        
        // 检查所属组织是否存在
        Organization organization = organizationRepository.findById(positionDTO.getOrganizationId())
            .orElseThrow(() -> new IllegalArgumentException("所属组织不存在: " + positionDTO.getOrganizationId()));
        
        if (organization.getDeleted()) {
            throw new IllegalArgumentException("所属组织已被删除: " + positionDTO.getOrganizationId());
        }
        
        Position position = positionDTO.toEntity();
        
        // 设置排序顺序
        if (position.getSortOrder() == null) {
            Integer maxSortOrder = positionRepository.findMaxSortOrderByOrganizationId(position.getOrganizationId());
            position.setSortOrder(maxSortOrder + 1);
        }
        
        // 设置默认值
        if (position.getCurrentCount() == null) {
            position.setCurrentCount(0);
        }
        
        Position savedPosition = positionRepository.save(position);
        log.info("岗位创建成功: ID={}, 名称={}", savedPosition.getId(), savedPosition.getPositionName());
        
        return PositionDTO.fromEntity(savedPosition);
    }
    
    @Override
    @Transactional
    public PositionDTO updatePosition(Long id, PositionDTO positionDTO) {
        log.info("更新岗位: ID={}, 名称={}", id, positionDTO.getPositionName());
        
        Position existingPosition = positionRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + id));
        
        if (existingPosition.getDeleted()) {
            throw new IllegalArgumentException("岗位已被删除，无法更新: " + id);
        }
        
        // 检查岗位编码是否已存在（排除当前岗位）
        if (positionRepository.existsByPositionCodeAndDeletedFalseAndIdNot(positionDTO.getPositionCode(), id)) {
            throw new IllegalArgumentException("岗位编码已存在: " + positionDTO.getPositionCode());
        }
        
        // 检查所属组织是否存在
        if (!positionDTO.getOrganizationId().equals(existingPosition.getOrganizationId())) {
            Organization organization = organizationRepository.findById(positionDTO.getOrganizationId())
                .orElseThrow(() -> new IllegalArgumentException("所属组织不存在: " + positionDTO.getOrganizationId()));
            
            if (organization.getDeleted()) {
                throw new IllegalArgumentException("所属组织已被删除: " + positionDTO.getOrganizationId());
            }
        }
        
        // 更新岗位信息
        existingPosition.setPositionCode(positionDTO.getPositionCode());
        existingPosition.setPositionName(positionDTO.getPositionName());
        existingPosition.setOrganizationId(positionDTO.getOrganizationId());
        existingPosition.setPositionType(positionDTO.getPositionType());
        existingPosition.setPositionLevel(positionDTO.getPositionLevel());
        existingPosition.setResponsibilities(positionDTO.getResponsibilities());
        existingPosition.setRequirements(positionDTO.getRequirements());
        existingPosition.setMinSalary(positionDTO.getMinSalary());
        existingPosition.setMaxSalary(positionDTO.getMaxSalary());
        existingPosition.setHeadcount(positionDTO.getHeadcount());
        existingPosition.setCurrentCount(positionDTO.getCurrentCount());
        existingPosition.setStatus(positionDTO.getStatus());
        
        Position savedPosition = positionRepository.save(existingPosition);
        log.info("岗位更新成功: ID={}, 名称={}", savedPosition.getId(), savedPosition.getPositionName());
        
        return PositionDTO.fromEntity(savedPosition);
    }
    
    @Override
    @Transactional
    public void deletePosition(Long id) {
        log.info("删除岗位: ID={}", id);
        
        Position position = positionRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + id));
        
        if (position.getDeleted()) {
            throw new IllegalArgumentException("岗位已被删除: " + id);
        }
        
        // 检查是否有在职人员
        if (position.getCurrentCount() != null && position.getCurrentCount() > 0) {
            throw new IllegalArgumentException("岗位有在职人员，无法删除");
        }
        
        // 软删除
        position.setDeleted(true);
        positionRepository.save(position);
        
        log.info("岗位删除成功: ID={}", id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PositionDTO getPositionById(Long id) {
        Position position = positionRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + id));
        
        if (position.getDeleted()) {
            throw new IllegalArgumentException("岗位已被删除: " + id);
        }
        
        return PositionDTO.fromEntity(position);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PositionDTO getPositionByCode(String positionCode) {
        Position position = positionRepository.findByPositionCodeAndDeletedFalse(positionCode)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + positionCode));
        
        return PositionDTO.fromEntity(position);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> queryPositions(PositionQueryDTO queryDTO) {
        // 构建排序
        Sort sort = Sort.by(
            "desc".equalsIgnoreCase(queryDTO.getSortDirection()) 
                ? Sort.Direction.DESC 
                : Sort.Direction.ASC,
            queryDTO.getSortBy()
        );
        
        Pageable pageable = PageRequest.of(queryDTO.getPage(), queryDTO.getSize(), sort);
        
        Page<Position> positionPage;
        
        if (queryDTO.getOnlyVacant() != null && queryDTO.getOnlyVacant()) {
            // 只查询空缺岗位
            positionPage = positionRepository.findVacantPositions(pageable);
        } else if (queryDTO.getOnlyOverstaffed() != null && queryDTO.getOnlyOverstaffed()) {
            // 只查询超编岗位 - 这里需要自定义实现
            List<Position> overstaffedPositions = positionRepository.findOverstaffedPositions();
            // 转换为Page对象的逻辑需要实现
            positionPage = positionRepository.findByConditions(
                queryDTO.getPositionName(),
                queryDTO.getPositionType(),
                queryDTO.getStatus(),
                queryDTO.getOrganizationId(),
                pageable
            );
        } else {
            positionPage = positionRepository.findByConditions(
                queryDTO.getPositionName(),
                queryDTO.getPositionType(),
                queryDTO.getStatus(),
                queryDTO.getOrganizationId(),
                pageable
            );
        }
        
        return positionPage.map(PositionDTO::fromEntity);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<PositionDTO> getPositionsByOrganization(Long organizationId) {
        List<Position> positions = positionRepository.findByOrganizationIdAndDeletedFalseOrderBySortOrderAsc(organizationId);
        return positions.stream()
            .map(PositionDTO::fromEntity)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<PositionDTO> getVacantPositions() {
        List<Position> vacantPositions = positionRepository.findVacantPositions();
        return vacantPositions.stream()
            .map(PositionDTO::fromEntity)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> getVacantPositions(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("sortOrder").ascending());
        Page<Position> vacantPositions = positionRepository.findVacantPositions(pageable);
        return vacantPositions.map(PositionDTO::fromEntity);
    }

    @Override
    @Transactional
    public PositionDTO updatePositionCount(Long id, Integer currentCount) {
        log.info("更新岗位人数: ID={}, 当前人数={}", id, currentCount);

        Position position = positionRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + id));

        if (position.getDeleted()) {
            throw new IllegalArgumentException("岗位已被删除: " + id);
        }

        if (currentCount < 0) {
            throw new IllegalArgumentException("当前人数不能小于0");
        }

        position.setCurrentCount(currentCount);
        Position savedPosition = positionRepository.save(position);

        log.info("岗位人数更新成功: ID={}, 当前人数={}", id, currentCount);

        return PositionDTO.fromEntity(savedPosition);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PositionDTO> getPositionsByOrganizationAndChildren(Long organizationId) {
        Organization organization = organizationRepository.findById(organizationId)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + organizationId));

        String parentPath = StringUtils.hasText(organization.getParentPath())
            ? organization.getParentPath() + "/" + organization.getId()
            : "/" + organization.getId();

        List<Position> positions = positionRepository.findByOrganizationAndChildren(organizationId, parentPath);
        return positions.stream()
            .map(PositionDTO::fromEntity)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void updateSortOrder(Long positionId, Integer sortOrder) {
        log.info("更新岗位排序: ID={}, 排序={}", positionId, sortOrder);

        Position position = positionRepository.findById(positionId)
            .orElseThrow(() -> new IllegalArgumentException("岗位不存在: " + positionId));

        if (position.getDeleted()) {
            throw new IllegalArgumentException("岗位已被删除: " + positionId);
        }

        position.setSortOrder(sortOrder);
        positionRepository.save(position);

        log.info("岗位排序更新成功: ID={}", positionId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByPositionCode(String positionCode, Long excludeId) {
        if (excludeId != null) {
            return positionRepository.existsByPositionCodeAndDeletedFalseAndIdNot(positionCode, excludeId);
        } else {
            return positionRepository.existsByPositionCodeAndDeletedFalse(positionCode);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Object getPositionStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总岗位数量
        long totalCount = positionRepository.count();
        statistics.put("totalCount", totalCount);

        // 各类型岗位数量
        List<Object[]> typeStats = positionRepository.countByPositionType();
        Map<String, Long> typeCountMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        statistics.put("typeStatistics", typeCountMap);

        // 编制和在职人数统计
        Object[] headcountStats = positionRepository.getTotalHeadcountAndCurrentCount();
        if (headcountStats != null && headcountStats.length >= 2) {
            statistics.put("totalHeadcount", headcountStats[0]);
            statistics.put("totalCurrentCount", headcountStats[1]);
        }

        // 空缺岗位数量
        long vacantCount = positionRepository.findVacantPositions().size();
        statistics.put("vacantCount", vacantCount);

        // 超编岗位数量
        long overstaffedCount = positionRepository.findOverstaffedPositions().size();
        statistics.put("overstaffedCount", overstaffedCount);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PositionDTO> getOverstaffedPositions() {
        List<Position> overstaffedPositions = positionRepository.findOverstaffedPositions();
        return overstaffedPositions.stream()
            .map(PositionDTO::fromEntity)
            .collect(Collectors.toList());
    }
}
