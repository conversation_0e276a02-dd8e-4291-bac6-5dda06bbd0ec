package com.hky.hr.organization.enums;

/**
 * 通用状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum Status {
    
    /**
     * 激活状态
     */
    ACTIVE("激活"),
    
    /**
     * 非激活状态
     */
    INACTIVE("非激活"),
    
    /**
     * 暂停状态
     */
    SUSPENDED("暂停"),
    
    /**
     * 已删除状态
     */
    DELETED("已删除");
    
    private final String description;
    
    Status(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据描述获取枚举值
     */
    public static Status fromDescription(String description) {
        for (Status status : values()) {
            if (status.description.equals(description)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的状态: " + description);
    }
}
