package com.hky.hr.organization.controller;

import com.hky.hr.organization.dto.ApiResponse;
import com.hky.hr.organization.dto.PositionDTO;
import com.hky.hr.organization.dto.PositionQueryDTO;
import com.hky.hr.organization.service.PositionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 岗位管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/positions")
@RequiredArgsConstructor
@Tag(name = "岗位管理", description = "岗位管理相关接口")
public class PositionController {
    
    private final PositionService positionService;
    
    /**
     * 创建岗位
     */
    @PostMapping
    @Operation(summary = "创建岗位", description = "创建新的岗位")
    public ResponseEntity<ApiResponse<PositionDTO>> createPosition(
            @Valid @RequestBody PositionDTO positionDTO) {
        try {
            PositionDTO result = positionService.createPosition(positionDTO);
            return ResponseEntity.ok(ApiResponse.success("岗位创建成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("创建岗位失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建岗位异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 更新岗位
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新岗位", description = "根据ID更新岗位信息")
    public ResponseEntity<ApiResponse<PositionDTO>> updatePosition(
            @Parameter(description = "岗位ID") @PathVariable Long id,
            @Valid @RequestBody PositionDTO positionDTO) {
        try {
            PositionDTO result = positionService.updatePosition(id, positionDTO);
            return ResponseEntity.ok(ApiResponse.success("岗位更新成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("更新岗位失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新岗位异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 删除岗位
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除岗位", description = "根据ID删除岗位（软删除）")
    public ResponseEntity<ApiResponse<Void>> deletePosition(
            @Parameter(description = "岗位ID") @PathVariable Long id) {
        try {
            positionService.deletePosition(id);
            return ResponseEntity.ok(ApiResponse.success("岗位删除成功", null));
        } catch (IllegalArgumentException e) {
            log.warn("删除岗位失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("删除岗位异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取岗位详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取岗位详情", description = "根据ID获取岗位详细信息")
    public ResponseEntity<ApiResponse<PositionDTO>> getPosition(
            @Parameter(description = "岗位ID") @PathVariable Long id) {
        try {
            PositionDTO result = positionService.getPositionById(id);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (IllegalArgumentException e) {
            log.warn("获取岗位详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("获取岗位详情异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 根据编码获取岗位
     */
    @GetMapping("/code/{positionCode}")
    @Operation(summary = "根据编码获取岗位", description = "根据岗位编码获取岗位信息")
    public ResponseEntity<ApiResponse<PositionDTO>> getPositionByCode(
            @Parameter(description = "岗位编码") @PathVariable String positionCode) {
        try {
            PositionDTO result = positionService.getPositionByCode(positionCode);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (IllegalArgumentException e) {
            log.warn("根据编码获取岗位失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("根据编码获取岗位异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 分页查询岗位
     */
    @GetMapping
    @Operation(summary = "分页查询岗位", description = "根据条件分页查询岗位")
    public ResponseEntity<ApiResponse<Page<PositionDTO>>> queryPositions(
            @Parameter(description = "查询条件") PositionQueryDTO queryDTO) {
        try {
            Page<PositionDTO> result = positionService.queryPositions(queryDTO);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("分页查询岗位异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取组织岗位列表
     */
    @GetMapping("/organization/{organizationId}")
    @Operation(summary = "获取组织岗位列表", description = "获取指定组织的岗位列表")
    public ResponseEntity<ApiResponse<List<PositionDTO>>> getPositionsByOrganization(
            @Parameter(description = "组织ID") @PathVariable Long organizationId) {
        try {
            List<PositionDTO> result = positionService.getPositionsByOrganization(organizationId);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取组织岗位列表异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取空缺岗位列表
     */
    @GetMapping("/vacant")
    @Operation(summary = "获取空缺岗位列表", description = "获取所有空缺岗位列表")
    public ResponseEntity<ApiResponse<Page<PositionDTO>>> getVacantPositions(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        try {
            Page<PositionDTO> result = positionService.getVacantPositions(page, size);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取空缺岗位列表异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 更新岗位人数
     */
    @PutMapping("/{id}/count")
    @Operation(summary = "更新岗位人数", description = "更新岗位的当前人数")
    public ResponseEntity<ApiResponse<PositionDTO>> updatePositionCount(
            @Parameter(description = "岗位ID") @PathVariable Long id,
            @Parameter(description = "当前人数") @RequestParam Integer currentCount) {
        try {
            PositionDTO result = positionService.updatePositionCount(id, currentCount);
            return ResponseEntity.ok(ApiResponse.success("岗位人数更新成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("更新岗位人数失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新岗位人数异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 更新排序
     */
    @PutMapping("/{id}/sort")
    @Operation(summary = "更新岗位排序", description = "更新岗位的排序顺序")
    public ResponseEntity<ApiResponse<Void>> updateSortOrder(
            @Parameter(description = "岗位ID") @PathVariable Long id,
            @Parameter(description = "排序值") @RequestParam Integer sortOrder) {
        try {
            positionService.updateSortOrder(id, sortOrder);
            return ResponseEntity.ok(ApiResponse.success("排序更新成功", null));
        } catch (IllegalArgumentException e) {
            log.warn("更新排序失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新排序异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取岗位统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取岗位统计信息", description = "获取岗位相关的统计数据")
    public ResponseEntity<ApiResponse<Object>> getStatistics() {
        try {
            Object result = positionService.getPositionStatistics();
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取岗位统计信息异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取超编岗位列表
     */
    @GetMapping("/overstaffed")
    @Operation(summary = "获取超编岗位列表", description = "获取所有超编岗位列表")
    public ResponseEntity<ApiResponse<List<PositionDTO>>> getOverstaffedPositions() {
        try {
            List<PositionDTO> result = positionService.getOverstaffedPositions();
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取超编岗位列表异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "岗位管理服务健康检查")
    public ResponseEntity<ApiResponse<String>> health() {
        return ResponseEntity.ok(ApiResponse.success("岗位管理服务运行正常"));
    }
}
