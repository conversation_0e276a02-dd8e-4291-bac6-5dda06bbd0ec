package com.hky.hr.organization.service;

import com.hky.hr.organization.dto.PositionDTO;
import com.hky.hr.organization.dto.PositionQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 岗位管理服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PositionService {
    
    /**
     * 创建岗位
     * 
     * @param positionDTO 岗位信息
     * @return 创建的岗位信息
     */
    PositionDTO createPosition(PositionDTO positionDTO);
    
    /**
     * 更新岗位
     * 
     * @param id 岗位ID
     * @param positionDTO 岗位信息
     * @return 更新后的岗位信息
     */
    PositionDTO updatePosition(Long id, PositionDTO positionDTO);
    
    /**
     * 删除岗位（软删除）
     * 
     * @param id 岗位ID
     */
    void deletePosition(Long id);
    
    /**
     * 根据ID获取岗位详情
     * 
     * @param id 岗位ID
     * @return 岗位信息
     */
    PositionDTO getPositionById(Long id);
    
    /**
     * 根据编码获取岗位详情
     * 
     * @param positionCode 岗位编码
     * @return 岗位信息
     */
    PositionDTO getPositionByCode(String positionCode);
    
    /**
     * 分页查询岗位
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<PositionDTO> queryPositions(PositionQueryDTO queryDTO);
    
    /**
     * 获取指定组织的岗位列表
     * 
     * @param organizationId 组织ID
     * @return 岗位列表
     */
    List<PositionDTO> getPositionsByOrganization(Long organizationId);
    
    /**
     * 获取空缺岗位列表
     * 
     * @return 空缺岗位列表
     */
    List<PositionDTO> getVacantPositions();
    
    /**
     * 获取空缺岗位列表（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    Page<PositionDTO> getVacantPositions(int page, int size);
    
    /**
     * 更新岗位人数
     * 
     * @param id 岗位ID
     * @param currentCount 当前人数
     * @return 更新后的岗位信息
     */
    PositionDTO updatePositionCount(Long id, Integer currentCount);
    
    /**
     * 获取指定组织及其子组织的所有岗位
     * 
     * @param organizationId 组织ID
     * @return 岗位列表
     */
    List<PositionDTO> getPositionsByOrganizationAndChildren(Long organizationId);
    
    /**
     * 更新岗位排序
     * 
     * @param positionId 岗位ID
     * @param sortOrder 新的排序值
     */
    void updateSortOrder(Long positionId, Integer sortOrder);
    
    /**
     * 检查岗位编码是否存在
     * 
     * @param positionCode 岗位编码
     * @param excludeId 排除的岗位ID
     * @return 是否存在
     */
    boolean existsByPositionCode(String positionCode, Long excludeId);
    
    /**
     * 获取岗位统计信息
     * 
     * @return 统计信息
     */
    Object getPositionStatistics();
    
    /**
     * 获取超编岗位列表
     * 
     * @return 超编岗位列表
     */
    List<PositionDTO> getOverstaffedPositions();
}
