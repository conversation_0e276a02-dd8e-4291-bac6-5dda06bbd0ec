package com.hky.hr.organization.service;

import com.hky.hr.organization.dto.OrganizationDTO;
import com.hky.hr.organization.dto.OrganizationQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 组织管理服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrganizationService {
    
    /**
     * 创建组织机构
     * 
     * @param organizationDTO 组织信息
     * @return 创建的组织信息
     */
    OrganizationDTO createOrganization(OrganizationDTO organizationDTO);
    
    /**
     * 更新组织机构
     * 
     * @param id 组织ID
     * @param organizationDTO 组织信息
     * @return 更新后的组织信息
     */
    OrganizationDTO updateOrganization(Long id, OrganizationDTO organizationDTO);
    
    /**
     * 删除组织机构（软删除）
     * 
     * @param id 组织ID
     */
    void deleteOrganization(Long id);
    
    /**
     * 根据ID获取组织详情
     * 
     * @param id 组织ID
     * @return 组织信息
     */
    OrganizationDTO getOrganizationById(Long id);
    
    /**
     * 根据编码获取组织详情
     * 
     * @param orgCode 组织编码
     * @return 组织信息
     */
    OrganizationDTO getOrganizationByCode(String orgCode);
    
    /**
     * 分页查询组织
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<OrganizationDTO> queryOrganizations(OrganizationQueryDTO queryDTO);
    
    /**
     * 获取组织架构树
     * 
     * @return 组织树结构
     */
    List<OrganizationDTO> getOrganizationTree();
    
    /**
     * 获取指定组织的子组织列表
     * 
     * @param parentId 父级组织ID
     * @return 子组织列表
     */
    List<OrganizationDTO> getChildrenOrganizations(Long parentId);
    
    /**
     * 获取指定组织的所有子组织（递归）
     * 
     * @param parentId 父级组织ID
     * @return 所有子组织列表
     */
    List<OrganizationDTO> getAllChildrenOrganizations(Long parentId);
    
    /**
     * 移动组织到新的父级下
     * 
     * @param organizationId 组织ID
     * @param newParentId 新父级ID
     */
    void moveOrganization(Long organizationId, Long newParentId);
    
    /**
     * 更新组织排序
     * 
     * @param organizationId 组织ID
     * @param sortOrder 新的排序值
     */
    void updateSortOrder(Long organizationId, Integer sortOrder);
    
    /**
     * 检查组织编码是否存在
     * 
     * @param orgCode 组织编码
     * @param excludeId 排除的组织ID
     * @return 是否存在
     */
    boolean existsByOrgCode(String orgCode, Long excludeId);
    
    /**
     * 获取组织统计信息
     * 
     * @return 统计信息
     */
    Object getOrganizationStatistics();
}
