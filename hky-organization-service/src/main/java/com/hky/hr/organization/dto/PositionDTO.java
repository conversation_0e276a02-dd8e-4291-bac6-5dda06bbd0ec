package com.hky.hr.organization.dto;

import com.hky.hr.organization.entity.Position;
import com.hky.hr.organization.enums.PositionType;
import com.hky.hr.organization.enums.Status;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 岗位数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PositionDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 岗位编码
     */
    @NotBlank(message = "岗位编码不能为空")
    @Size(max = 64, message = "岗位编码长度不能超过64个字符")
    private String positionCode;
    
    /**
     * 岗位名称
     */
    @NotBlank(message = "岗位名称不能为空")
    @Size(max = 128, message = "岗位名称长度不能超过128个字符")
    private String positionName;
    
    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织不能为空")
    private Long organizationId;
    
    /**
     * 所属组织名称
     */
    private String organizationName;
    
    /**
     * 岗位类型
     */
    @NotNull(message = "岗位类型不能为空")
    private PositionType positionType;
    
    /**
     * 岗位级别
     */
    @Size(max = 32, message = "岗位级别长度不能超过32个字符")
    private String positionLevel;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 岗位职责
     */
    private String responsibilities;
    
    /**
     * 任职要求
     */
    private String requirements;
    
    /**
     * 最低薪资
     */
    @DecimalMin(value = "0.00", message = "最低薪资不能小于0")
    private BigDecimal minSalary;
    
    /**
     * 最高薪资
     */
    @DecimalMin(value = "0.00", message = "最高薪资不能小于0")
    private BigDecimal maxSalary;
    
    /**
     * 编制人数
     */
    @Min(value = 0, message = "编制人数不能小于0")
    private Integer headcount;
    
    /**
     * 当前人数
     */
    @Min(value = 0, message = "当前人数不能小于0")
    private Integer currentCount;
    
    /**
     * 空缺人数
     */
    private Integer vacantCount;
    
    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Status status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 版本号
     */
    private Long version;
    
    /**
     * 从实体类转换为DTO
     */
    public static PositionDTO fromEntity(Position entity) {
        if (entity == null) {
            return null;
        }
        
        PositionDTO dto = new PositionDTO();
        dto.setId(entity.getId());
        dto.setPositionCode(entity.getPositionCode());
        dto.setPositionName(entity.getPositionName());
        dto.setOrganizationId(entity.getOrganizationId());
        dto.setPositionType(entity.getPositionType());
        dto.setPositionLevel(entity.getPositionLevel());
        dto.setSortOrder(entity.getSortOrder());
        dto.setResponsibilities(entity.getResponsibilities());
        dto.setRequirements(entity.getRequirements());
        dto.setMinSalary(entity.getMinSalary());
        dto.setMaxSalary(entity.getMaxSalary());
        dto.setHeadcount(entity.getHeadcount());
        dto.setCurrentCount(entity.getCurrentCount());
        dto.setVacantCount(entity.getVacantCount());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime());
        dto.setCreateBy(entity.getCreateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setVersion(entity.getVersion());
        
        // 设置组织名称（如果有关联）
        if (entity.getOrganization() != null) {
            dto.setOrganizationName(entity.getOrganization().getOrgName());
        }
        
        return dto;
    }
    
    /**
     * 转换为实体类
     */
    public Position toEntity() {
        Position entity = new Position();
        entity.setId(this.id);
        entity.setPositionCode(this.positionCode);
        entity.setPositionName(this.positionName);
        entity.setOrganizationId(this.organizationId);
        entity.setPositionType(this.positionType);
        entity.setPositionLevel(this.positionLevel);
        entity.setSortOrder(this.sortOrder);
        entity.setResponsibilities(this.responsibilities);
        entity.setRequirements(this.requirements);
        entity.setMinSalary(this.minSalary);
        entity.setMaxSalary(this.maxSalary);
        entity.setHeadcount(this.headcount);
        entity.setCurrentCount(this.currentCount);
        entity.setStatus(this.status);
        entity.setVersion(this.version);
        
        return entity;
    }
}
