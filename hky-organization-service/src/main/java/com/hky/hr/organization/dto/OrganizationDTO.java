package com.hky.hr.organization.dto;

import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.Status;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 组织机构数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 组织编码
     */
    @NotBlank(message = "组织编码不能为空")
    @Size(max = 64, message = "组织编码长度不能超过64个字符")
    private String orgCode;
    
    /**
     * 组织名称
     */
    @NotBlank(message = "组织名称不能为空")
    @Size(max = 128, message = "组织名称长度不能超过128个字符")
    private String orgName;
    
    /**
     * 组织简称
     */
    @Size(max = 128, message = "组织简称长度不能超过128个字符")
    private String orgShortName;
    
    /**
     * 组织类型
     */
    @NotNull(message = "组织类型不能为空")
    private OrganizationType orgType;
    
    /**
     * 父级组织ID
     */
    private Long parentId;
    
    /**
     * 组织层级
     */
    private Integer orgLevel;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 负责人
     */
    @Size(max = 128, message = "负责人姓名长度不能超过128个字符")
    private String leader;
    
    /**
     * 联系电话
     */
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String phone;
    
    /**
     * 邮箱地址
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 128, message = "邮箱长度不能超过128个字符")
    private String email;
    
    /**
     * 地址
     */
    @Size(max = 256, message = "地址长度不能超过256个字符")
    private String address;
    
    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Status status;
    
    /**
     * 生效日期
     */
    private LocalDate effectiveDate;
    
    /**
     * 撤销日期
     */
    private LocalDate withdrawDate;
    
    /**
     * 批文号
     */
    @Size(max = 128, message = "批文号长度不能超过128个字符")
    private String approvalDocNumber;
    
    /**
     * 批准日期
     */
    private LocalDate approvalDate;
    
    /**
     * 设立日期
     */
    private LocalDate establishmentDate;
    
    /**
     * 是否虚拟部门
     */
    private Boolean isVirtual;
    
    /**
     * 是否临时机构
     */
    private Boolean isTemporary;
    
    /**
     * 父级路径
     */
    private String parentPath;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 版本号
     */
    private Long version;
    
    /**
     * 子组织列表
     */
    private List<OrganizationDTO> children;
    
    /**
     * 岗位数量
     */
    private Long positionCount;
    
    /**
     * 从实体类转换为DTO
     */
    public static OrganizationDTO fromEntity(Organization entity) {
        if (entity == null) {
            return null;
        }
        
        OrganizationDTO dto = new OrganizationDTO();
        dto.setId(entity.getId());
        dto.setOrgCode(entity.getOrgCode());
        dto.setOrgName(entity.getOrgName());
        dto.setOrgShortName(entity.getOrgShortName());
        dto.setOrgType(entity.getOrgType());
        dto.setParentId(entity.getParentId());
        dto.setOrgLevel(entity.getOrgLevel());
        dto.setSortOrder(entity.getSortOrder());
        dto.setDescription(entity.getDescription());
        dto.setLeader(entity.getLeader());
        dto.setPhone(entity.getPhone());
        dto.setEmail(entity.getEmail());
        dto.setAddress(entity.getAddress());
        dto.setStatus(entity.getStatus());
        dto.setEffectiveDate(entity.getEffectiveDate());
        dto.setWithdrawDate(entity.getWithdrawDate());
        dto.setApprovalDocNumber(entity.getApprovalDocNumber());
        dto.setApprovalDate(entity.getApprovalDate());
        dto.setEstablishmentDate(entity.getEstablishmentDate());
        dto.setIsVirtual(entity.getIsVirtual());
        dto.setIsTemporary(entity.getIsTemporary());
        dto.setParentPath(entity.getParentPath());
        dto.setRemark(entity.getRemark());
        dto.setCreateTime(entity.getCreateTime());
        dto.setCreateBy(entity.getCreateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setVersion(entity.getVersion());
        
        return dto;
    }
    
    /**
     * 转换为实体类
     */
    public Organization toEntity() {
        Organization entity = new Organization();
        entity.setId(this.id);
        entity.setOrgCode(this.orgCode);
        entity.setOrgName(this.orgName);
        entity.setOrgShortName(this.orgShortName);
        entity.setOrgType(this.orgType);
        entity.setParentId(this.parentId);
        entity.setOrgLevel(this.orgLevel);
        entity.setSortOrder(this.sortOrder);
        entity.setDescription(this.description);
        entity.setLeader(this.leader);
        entity.setPhone(this.phone);
        entity.setEmail(this.email);
        entity.setAddress(this.address);
        entity.setStatus(this.status);
        entity.setEffectiveDate(this.effectiveDate);
        entity.setWithdrawDate(this.withdrawDate);
        entity.setApprovalDocNumber(this.approvalDocNumber);
        entity.setApprovalDate(this.approvalDate);
        entity.setEstablishmentDate(this.establishmentDate);
        entity.setIsVirtual(this.isVirtual);
        entity.setIsTemporary(this.isTemporary);
        entity.setParentPath(this.parentPath);
        entity.setRemark(this.remark);
        entity.setVersion(this.version);
        
        return entity;
    }
}
