package com.hky.hr.organization.controller;

import com.hky.hr.organization.dto.ApiResponse;
import com.hky.hr.organization.dto.OrganizationDTO;
import com.hky.hr.organization.dto.OrganizationQueryDTO;
import com.hky.hr.organization.service.OrganizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 组织管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/organizations")
@RequiredArgsConstructor
@Tag(name = "组织管理", description = "组织架构管理相关接口")
public class OrganizationController {
    
    private final OrganizationService organizationService;
    
    /**
     * 创建组织机构
     */
    @PostMapping
    @Operation(summary = "创建组织机构", description = "创建新的组织机构")
    public ResponseEntity<ApiResponse<OrganizationDTO>> createOrganization(
            @Valid @RequestBody OrganizationDTO organizationDTO) {
        try {
            OrganizationDTO result = organizationService.createOrganization(organizationDTO);
            return ResponseEntity.ok(ApiResponse.success("组织机构创建成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("创建组织机构失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建组织机构异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 更新组织机构
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新组织机构", description = "根据ID更新组织机构信息")
    public ResponseEntity<ApiResponse<OrganizationDTO>> updateOrganization(
            @Parameter(description = "组织ID") @PathVariable Long id,
            @Valid @RequestBody OrganizationDTO organizationDTO) {
        try {
            OrganizationDTO result = organizationService.updateOrganization(id, organizationDTO);
            return ResponseEntity.ok(ApiResponse.success("组织机构更新成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("更新组织机构失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新组织机构异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 删除组织机构
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除组织机构", description = "根据ID删除组织机构（软删除）")
    public ResponseEntity<ApiResponse<Void>> deleteOrganization(
            @Parameter(description = "组织ID") @PathVariable Long id) {
        try {
            organizationService.deleteOrganization(id);
            return ResponseEntity.ok(ApiResponse.success("组织机构删除成功", null));
        } catch (IllegalArgumentException e) {
            log.warn("删除组织机构失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("删除组织机构异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取组织详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取组织详情", description = "根据ID获取组织机构详细信息")
    public ResponseEntity<ApiResponse<OrganizationDTO>> getOrganization(
            @Parameter(description = "组织ID") @PathVariable Long id) {
        try {
            OrganizationDTO result = organizationService.getOrganizationById(id);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (IllegalArgumentException e) {
            log.warn("获取组织详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("获取组织详情异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 根据编码获取组织
     */
    @GetMapping("/code/{orgCode}")
    @Operation(summary = "根据编码获取组织", description = "根据组织编码获取组织机构信息")
    public ResponseEntity<ApiResponse<OrganizationDTO>> getOrganizationByCode(
            @Parameter(description = "组织编码") @PathVariable String orgCode) {
        try {
            OrganizationDTO result = organizationService.getOrganizationByCode(orgCode);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (IllegalArgumentException e) {
            log.warn("根据编码获取组织失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("根据编码获取组织异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 分页查询组织
     */
    @GetMapping
    @Operation(summary = "分页查询组织", description = "根据条件分页查询组织机构")
    public ResponseEntity<ApiResponse<Page<OrganizationDTO>>> queryOrganizations(
            @Parameter(description = "查询条件") OrganizationQueryDTO queryDTO) {
        try {
            Page<OrganizationDTO> result = organizationService.queryOrganizations(queryDTO);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("分页查询组织异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取组织架构树
     */
    @GetMapping("/tree")
    @Operation(summary = "获取组织架构树", description = "获取完整的组织架构树形结构")
    public ResponseEntity<ApiResponse<List<OrganizationDTO>>> getOrganizationTree() {
        try {
            List<OrganizationDTO> result = organizationService.getOrganizationTree();
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取组织架构树异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取子组织列表
     */
    @GetMapping("/{parentId}/children")
    @Operation(summary = "获取子组织列表", description = "获取指定组织的直接子组织列表")
    public ResponseEntity<ApiResponse<List<OrganizationDTO>>> getChildrenOrganizations(
            @Parameter(description = "父级组织ID") @PathVariable Long parentId) {
        try {
            List<OrganizationDTO> result = organizationService.getChildrenOrganizations(parentId);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取子组织列表异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 移动组织
     */
    @PutMapping("/{id}/move")
    @Operation(summary = "移动组织", description = "将组织移动到新的父级组织下")
    public ResponseEntity<ApiResponse<Void>> moveOrganization(
            @Parameter(description = "组织ID") @PathVariable Long id,
            @Parameter(description = "新父级组织ID") @RequestParam(required = false) Long newParentId) {
        try {
            organizationService.moveOrganization(id, newParentId);
            return ResponseEntity.ok(ApiResponse.success("组织移动成功", null));
        } catch (IllegalArgumentException e) {
            log.warn("移动组织失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("移动组织异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 更新排序
     */
    @PutMapping("/{id}/sort")
    @Operation(summary = "更新组织排序", description = "更新组织的排序顺序")
    public ResponseEntity<ApiResponse<Void>> updateSortOrder(
            @Parameter(description = "组织ID") @PathVariable Long id,
            @Parameter(description = "排序值") @RequestParam Integer sortOrder) {
        try {
            organizationService.updateSortOrder(id, sortOrder);
            return ResponseEntity.ok(ApiResponse.success("排序更新成功", null));
        } catch (IllegalArgumentException e) {
            log.warn("更新排序失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新排序异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 获取组织统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取组织统计信息", description = "获取组织相关的统计数据")
    public ResponseEntity<ApiResponse<Object>> getStatistics() {
        try {
            Object result = organizationService.getOrganizationStatistics();
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取组织统计信息异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统异常，请稍后重试"));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "组织管理服务健康检查")
    public ResponseEntity<ApiResponse<String>> health() {
        return ResponseEntity.ok(ApiResponse.success("组织管理服务运行正常"));
    }
}
