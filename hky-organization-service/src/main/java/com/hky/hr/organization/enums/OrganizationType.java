package com.hky.hr.organization.enums;

/**
 * 组织类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrganizationType {
    
    /**
     * 学校
     */
    SCHOOL("学校"),
    
    /**
     * 学院
     */
    COLLEGE("学院"),
    
    /**
     * 部门
     */
    DEPARTMENT("部门"),
    
    /**
     * 科室
     */
    OFFICE("科室"),
    
    /**
     * 中心
     */
    CENTER("中心"),
    
    /**
     * 研究所
     */
    INSTITUTE("研究所"),
    
    /**
     * 实验室
     */
    LABORATORY("实验室"),
    
    /**
     * 其他
     */
    OTHER("其他");
    
    private final String description;
    
    OrganizationType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据描述获取枚举值
     */
    public static OrganizationType fromDescription(String description) {
        for (OrganizationType type : values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的组织类型: " + description);
    }
}
