package com.hky.hr.organization.repository;

import com.hky.hr.organization.entity.Position;
import com.hky.hr.organization.enums.PositionType;
import com.hky.hr.organization.enums.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 岗位数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface PositionRepository extends JpaRepository<Position, Long> {
    
    /**
     * 根据岗位编码查找岗位（未删除）
     */
    Optional<Position> findByPositionCodeAndDeletedFalse(String positionCode);
    
    /**
     * 根据组织ID查找岗位列表（未删除）
     */
    List<Position> findByOrganizationIdAndDeletedFalseOrderBySortOrderAsc(Long organizationId);
    
    /**
     * 根据岗位类型查找岗位列表（未删除）
     */
    List<Position> findByPositionTypeAndDeletedFalseOrderBySortOrderAsc(PositionType positionType);
    
    /**
     * 根据状态查找岗位列表（未删除）
     */
    List<Position> findByStatusAndDeletedFalseOrderBySortOrderAsc(Status status);
    
    /**
     * 根据岗位名称模糊查询（未删除）
     */
    Page<Position> findByPositionNameContainingAndDeletedFalse(String positionName, Pageable pageable);
    
    /**
     * 多条件查询岗位
     */
    @Query("SELECT p FROM Position p WHERE " +
           "(:positionName IS NULL OR p.positionName LIKE %:positionName%) AND " +
           "(:positionType IS NULL OR p.positionType = :positionType) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:organizationId IS NULL OR p.organizationId = :organizationId) AND " +
           "p.deleted = false " +
           "ORDER BY p.sortOrder ASC")
    Page<Position> findByConditions(@Param("positionName") String positionName,
                                  @Param("positionType") PositionType positionType,
                                  @Param("status") Status status,
                                  @Param("organizationId") Long organizationId,
                                  Pageable pageable);
    
    /**
     * 查找空缺岗位（编制人数大于当前人数且未删除）
     */
    @Query("SELECT p FROM Position p WHERE " +
           "p.headcount > p.currentCount AND " +
           "p.status = 'ACTIVE' AND " +
           "p.deleted = false " +
           "ORDER BY p.sortOrder ASC")
    List<Position> findVacantPositions();
    
    /**
     * 查找空缺岗位（分页）
     */
    @Query("SELECT p FROM Position p WHERE " +
           "p.headcount > p.currentCount AND " +
           "p.status = 'ACTIVE' AND " +
           "p.deleted = false " +
           "ORDER BY p.sortOrder ASC")
    Page<Position> findVacantPositions(Pageable pageable);
    
    /**
     * 查找指定组织及其子组织的所有岗位
     */
    @Query("SELECT p FROM Position p " +
           "JOIN Organization o ON p.organizationId = o.id " +
           "WHERE (o.id = :organizationId OR o.parentPath LIKE CONCAT(:parentPath, '%')) AND " +
           "p.deleted = false AND o.deleted = false " +
           "ORDER BY p.sortOrder ASC")
    List<Position> findByOrganizationAndChildren(@Param("organizationId") Long organizationId,
                                               @Param("parentPath") String parentPath);
    
    /**
     * 统计指定组织下的岗位数量（未删除）
     */
    long countByOrganizationIdAndDeletedFalse(Long organizationId);
    
    /**
     * 检查岗位编码是否存在（未删除，排除指定ID）
     */
    boolean existsByPositionCodeAndDeletedFalseAndIdNot(String positionCode, Long id);
    
    /**
     * 检查岗位编码是否存在（未删除）
     */
    boolean existsByPositionCodeAndDeletedFalse(String positionCode);
    
    /**
     * 查找最大排序值
     */
    @Query("SELECT COALESCE(MAX(p.sortOrder), 0) FROM Position p WHERE " +
           "p.organizationId = :organizationId AND p.deleted = false")
    Integer findMaxSortOrderByOrganizationId(@Param("organizationId") Long organizationId);
    
    /**
     * 统计各岗位类型的数量
     */
    @Query("SELECT p.positionType, COUNT(p) FROM Position p WHERE " +
           "p.deleted = false GROUP BY p.positionType")
    List<Object[]> countByPositionType();
    
    /**
     * 统计总编制人数和当前人数
     */
    @Query("SELECT SUM(p.headcount), SUM(p.currentCount) FROM Position p WHERE " +
           "p.deleted = false AND p.status = 'ACTIVE'")
    Object[] getTotalHeadcountAndCurrentCount();
    
    /**
     * 查找超编岗位（当前人数大于编制人数且未删除）
     */
    @Query("SELECT p FROM Position p WHERE " +
           "p.currentCount > p.headcount AND " +
           "p.status = 'ACTIVE' AND " +
           "p.deleted = false " +
           "ORDER BY p.sortOrder ASC")
    List<Position> findOverstaffedPositions();
}
