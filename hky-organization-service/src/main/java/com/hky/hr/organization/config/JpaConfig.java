package com.hky.hr.organization.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.Optional;

/**
 * JPA配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
@EnableJpaRepositories(basePackages = "com.hky.hr.organization.repository")
public class JpaConfig {
    
    /**
     * 审计信息提供者
     * 在实际项目中，这里应该从安全上下文中获取当前用户信息
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return new AuditorAware<String>() {
            @Override
            public Optional<String> getCurrentAuditor() {
                // TODO: 从Spring Security上下文中获取当前用户
                // SecurityContext context = SecurityContextHolder.getContext();
                // Authentication authentication = context.getAuthentication();
                // if (authentication != null && authentication.isAuthenticated()) {
                //     return Optional.of(authentication.getName());
                // }
                
                // 临时返回系统用户
                return Optional.of("system");
            }
        };
    }
}
