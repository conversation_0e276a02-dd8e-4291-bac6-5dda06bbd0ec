package com.hky.hr.organization.enums;

/**
 * 岗位类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PositionType {
    
    /**
     * 管理岗位
     */
    MANAGEMENT("管理岗位"),
    
    /**
     * 教学岗位
     */
    TEACHING("教学岗位"),
    
    /**
     * 科研岗位
     */
    RESEARCH("科研岗位"),
    
    /**
     * 教辅岗位
     */
    TEACHING_SUPPORT("教辅岗位"),
    
    /**
     * 工勤岗位
     */
    SERVICE("工勤岗位"),
    
    /**
     * 专业技术岗位
     */
    PROFESSIONAL("专业技术岗位"),
    
    /**
     * 临时岗位
     */
    TEMPORARY("临时岗位"),
    
    /**
     * 其他
     */
    OTHER("其他");
    
    private final String description;
    
    PositionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据描述获取枚举值
     */
    public static PositionType fromDescription(String description) {
        for (PositionType type : values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的岗位类型: " + description);
    }
}
