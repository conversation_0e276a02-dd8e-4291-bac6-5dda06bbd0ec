package com.hky.hr.organization.repository;

import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 组织机构数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface OrganizationRepository extends JpaRepository<Organization, Long> {
    
    /**
     * 根据组织编码查找组织（未删除）
     */
    Optional<Organization> findByOrgCodeAndDeletedFalse(String orgCode);
    
    /**
     * 根据父级ID查找子组织列表（未删除）
     */
    List<Organization> findByParentIdAndDeletedFalseOrderBySortOrderAsc(Long parentId);
    
    /**
     * 根据组织类型查找组织列表（未删除）
     */
    List<Organization> findByOrgTypeAndDeletedFalseOrderBySortOrderAsc(OrganizationType orgType);
    
    /**
     * 根据状态查找组织列表（未删除）
     */
    List<Organization> findByStatusAndDeletedFalseOrderBySortOrderAsc(Status status);
    
    /**
     * 查找根组织列表（父级ID为空且未删除）
     */
    List<Organization> findByParentIdIsNullAndDeletedFalseOrderBySortOrderAsc();
    
    /**
     * 根据组织名称模糊查询（未删除）
     */
    Page<Organization> findByOrgNameContainingAndDeletedFalse(String orgName, Pageable pageable);
    
    /**
     * 多条件查询组织
     */
    @Query("SELECT o FROM Organization o WHERE " +
           "(:orgName IS NULL OR o.orgName LIKE %:orgName%) AND " +
           "(:orgType IS NULL OR o.orgType = :orgType) AND " +
           "(:status IS NULL OR o.status = :status) AND " +
           "(:parentId IS NULL OR o.parentId = :parentId) AND " +
           "o.deleted = false " +
           "ORDER BY o.sortOrder ASC")
    Page<Organization> findByConditions(@Param("orgName") String orgName,
                                      @Param("orgType") OrganizationType orgType,
                                      @Param("status") Status status,
                                      @Param("parentId") Long parentId,
                                      Pageable pageable);
    
    /**
     * 查找指定组织的所有子组织（递归查询）
     */
    @Query("SELECT o FROM Organization o WHERE " +
           "o.parentPath LIKE CONCAT(:parentPath, '%') AND " +
           "o.deleted = false " +
           "ORDER BY o.orgLevel ASC, o.sortOrder ASC")
    List<Organization> findAllChildrenByParentPath(@Param("parentPath") String parentPath);
    
    /**
     * 统计指定父级下的子组织数量（未删除）
     */
    long countByParentIdAndDeletedFalse(Long parentId);
    
    /**
     * 检查组织编码是否存在（未删除，排除指定ID）
     */
    boolean existsByOrgCodeAndDeletedFalseAndIdNot(String orgCode, Long id);
    
    /**
     * 检查组织编码是否存在（未删除）
     */
    boolean existsByOrgCodeAndDeletedFalse(String orgCode);
    
    /**
     * 查找指定层级的所有组织（未删除）
     */
    List<Organization> findByOrgLevelAndDeletedFalseOrderBySortOrderAsc(Integer orgLevel);
    
    /**
     * 查找最大排序值
     */
    @Query("SELECT COALESCE(MAX(o.sortOrder), 0) FROM Organization o WHERE " +
           "o.parentId = :parentId AND o.deleted = false")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);
    
    /**
     * 根据负责人查找组织列表（未删除）
     */
    List<Organization> findByLeaderAndDeletedFalseOrderBySortOrderAsc(String leader);
    
    /**
     * 查找所有未删除的组织，按层级和排序排列
     */
    @Query("SELECT o FROM Organization o WHERE o.deleted = false " +
           "ORDER BY o.orgLevel ASC, o.sortOrder ASC")
    List<Organization> findAllActiveOrderByLevelAndSort();
}
