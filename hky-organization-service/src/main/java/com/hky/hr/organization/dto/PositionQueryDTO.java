package com.hky.hr.organization.dto;

import com.hky.hr.organization.enums.PositionType;
import com.hky.hr.organization.enums.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 岗位查询条件DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PositionQueryDTO {
    
    /**
     * 岗位名称（模糊查询）
     */
    private String positionName;
    
    /**
     * 岗位编码
     */
    private String positionCode;
    
    /**
     * 岗位类型
     */
    private PositionType positionType;
    
    /**
     * 状态
     */
    private Status status;
    
    /**
     * 所属组织ID
     */
    private Long organizationId;
    
    /**
     * 岗位级别
     */
    private String positionLevel;
    
    /**
     * 是否只查询空缺岗位
     */
    private Boolean onlyVacant = false;
    
    /**
     * 是否只查询超编岗位
     */
    private Boolean onlyOverstaffed = false;
    
    /**
     * 页码（从0开始）
     */
    private Integer page = 0;
    
    /**
     * 每页大小
     */
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sortBy = "sortOrder";
    
    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection = "asc";
}
